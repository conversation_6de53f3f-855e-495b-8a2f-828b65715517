package com.vance.demo.util.common;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.Test;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class FileUtilTest {
    private static final File DESKTOP = new File(System.getProperty("user.home"), "Desktop");

    @Test
    void testBase64ToFile() {
        try {
            String img = FileUtils.readFileToString(FileUtils.getFile(DESKTOP, "新增文字文件.txt"),
                    StandardCharsets.UTF_8);
            File outFile = FileUtils.getFile(DESKTOP, "test.jpg");
            FileUtil.base64ToFile(img, outFile);
            log.debug("產出檔案：{}", outFile);
        } catch (IOException e) {
            log.error("{}", e);
        }
    }
}
