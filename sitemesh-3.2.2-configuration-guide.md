# Sitemesh 3.2.2 配置指南

## 📋 概述

Sitemesh 是一個網頁佈局和裝飾框架，用於建立具有一致外觀、導航和佈局的網站。Sitemesh 3.2.2 是最新版本，支援 Java 17+ 和 Jakarta EE 10。

### 🎯 主要特性

- **零配置啟動**: 只需放入 JAR 檔案即可使用
- **高效能**: 比 Sitemesh 2 快 3 倍，記憶體使用量減半
- **裝飾器鏈**: 支援多層裝飾器
- **Spring Boot 支援**: 提供 Spring Boot Starter
- **離線網站生成**: 支援靜態網站生成

## 🚀 安裝配置

### 系統需求

- **Java**: JDK 17 或更高版本
- **Servlet 容器**: Servlet 3.x 相容容器
- **Jakarta EE**: 支援 Jakarta EE 10

### Maven 依賴

```xml
<dependencies>
    <!-- 標準 Sitemesh 依賴 -->
    <dependency>
        <groupId>org.sitemesh</groupId>
        <artifactId>sitemesh</artifactId>
        <version>3.2.1</version>
    </dependency>
</dependencies>
```

### Gradle 依賴

```gradle
dependencies {
    runtimeOnly 'org.sitemesh:sitemesh:3.2.1'
}
```

### Spring Boot Starter

```xml
<dependency>
    <groupId>org.sitemesh</groupId>
    <artifactId>spring-boot-starter-sitemesh</artifactId>
    <version>3.2.1</version>
</dependency>
```

```gradle
dependencies {
    runtimeOnly 'org.sitemesh:spring-boot-starter-sitemesh:3.2.1'
}
```

## ⚙️ 基本配置

### 1. Web.xml 配置

在 `/WEB-INF/web.xml` 中新增 Sitemesh Filter：

```xml
<web-app>
    <filter>
        <filter-name>sitemesh</filter-name>
        <filter-class>org.sitemesh.config.ConfigurableSiteMeshFilter</filter-class>
    </filter>

    <filter-mapping>
        <filter-name>sitemesh</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>
</web-app>
```

### 2. 建立裝飾器

建立 `/decorator.html` 檔案：

```html
<html>
<head>
    <title>網站標題: <sitemesh:write property="title"/></title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            background-color: #f5f5f5; 
        }
        .header { 
            background-color: #333; 
            color: white; 
            padding: 20px; 
            text-align: center; 
        }
        .content { 
            padding: 20px; 
            background-color: white; 
            margin: 20px; 
            border-radius: 5px; 
        }
        .footer { 
            text-align: center; 
            padding: 10px; 
            color: #666; 
        }
    </style>
    <sitemesh:write property="head"/>
</head>
<body>
    <div class="header">
        <h1>我的網站: <sitemesh:write property="title"/></h1>
    </div>
    
    <div class="content">
        <sitemesh:write property="body"/>
    </div>
    
    <div class="footer">
        <p>&copy; 2025 我的公司. 版權所有.</p>
    </div>
</body>
</html>
```

## 🔧 配置方式

### 1. XML 配置

建立 `/WEB-INF/sitemesh3.xml`：

```xml
<sitemesh>
    <!-- 預設裝飾器 -->
    <mapping decorator="decorator.html"/>
    
    <!-- 路徑特定裝飾器 -->
    <mapping path="/admin/*" decorator="admin-decorator.html"/>
    <mapping path="/*.special.jsp" decorator="special-decorator.html"/>
    
    <!-- 多重裝飾器 -->
    <mapping>
        <path>/articles/*</path>
        <decorator>article.html</decorator>
        <decorator>two-page-layout.html</decorator>
        <decorator>common.html</decorator>
    </mapping>
    
    <!-- 排除路徑 -->
    <mapping path="/api/*" exclude="true"/>
    <mapping path="/static/*" exclude="true"/>
    
    <!-- MIME 類型配置 -->
    <mime-type>text/html</mime-type>
    <mime-type>application/xhtml+xml</mime-type>
</sitemesh>
```

### 2. Java 配置

建立自訂 Filter 類別：

```java
public class MySiteMeshFilter extends ConfigurableSiteMeshFilter {
    
    @Override
    protected void applyCustomConfiguration(SiteMeshFilterBuilder builder) {
        builder
            // 預設裝飾器
            .addDecoratorPath("/*", "decorator.html")
            
            // 路徑特定裝飾器
            .addDecoratorPath("/admin/*", "admin-decorator.html")
            .addDecoratorPath("/*.special.jsp", "special-decorator.html")
            
            // 多重裝飾器
            .addDecoratorPaths("/articles/*", 
                "article.html", 
                "two-page-layout.html", 
                "common.html")
            
            // 排除路徑
            .addExcludedPath("/api/*")
            .addExcludedPath("/static/*")
            
            // MIME 類型
            .setMimeTypes("text/html", "application/xhtml+xml");
    }
}
```

在 `web.xml` 中使用自訂 Filter：

```xml
<filter>
    <filter-name>sitemesh</filter-name>
    <filter-class>com.example.MySiteMeshFilter</filter-class>
</filter>
```

### 3. HTML Meta 標籤配置

在頁面中直接指定裝飾器：

```html
<html>
<head>
    <title>Hello World</title>
    <meta name="decorator" content="decorator.html"/>
</head>
<body>
    <h1>這個頁面將被裝飾 :)</h1>
</body>
</html>
```

## 🎨 進階配置

### 1. 自訂標籤規則

```java
public class MySiteMeshFilter extends ConfigurableSiteMeshFilter {
    
    @Override
    protected void applyCustomConfiguration(SiteMeshFilterBuilder builder) {
        builder.addTagRuleBundles(
            new CssCompressingBundle(), 
            new LinkRewritingBundle()
        );
    }
}
```

### 2. 裝飾器屬性

在裝飾器中可以使用的屬性：

- `<sitemesh:write property="title"/>` - 頁面標題
- `<sitemesh:write property="head"/>` - head 區塊內容
- `<sitemesh:write property="body"/>` - body 區塊內容
- `<sitemesh:write property="meta.description"/>` - meta 描述
- `<sitemesh:write property="meta.keywords"/>` - meta 關鍵字

### 3. 條件裝飾

```xml
<sitemesh>
    <!-- 根據使用者代理選擇裝飾器 -->
    <mapping path="/*" decorator="mobile-decorator.html">
        <condition property="user-agent" contains="Mobile"/>
    </mapping>
    
    <!-- 預設裝飾器 -->
    <mapping path="/*" decorator="desktop-decorator.html"/>
</sitemesh>
```

## 🏗️ Spring Boot 整合

### 自動配置

使用 Spring Boot Starter 時，Sitemesh 會自動配置。只需在 `application.properties` 中設定：

```properties
# 啟用 Sitemesh
sitemesh.enabled=true

# 預設裝飾器
sitemesh.default-decorator=decorator.html

# 排除路徑
sitemesh.excluded-paths=/api/*,/static/*
```

### 自訂配置

```java
@Configuration
public class SitemeshConfig {
    
    @Bean
    public FilterRegistrationBean<ConfigurableSiteMeshFilter> siteMeshFilter() {
        FilterRegistrationBean<ConfigurableSiteMeshFilter> filter = 
            new FilterRegistrationBean<>();
        
        filter.setFilter(new ConfigurableSiteMeshFilter() {
            @Override
            protected void applyCustomConfiguration(SiteMeshFilterBuilder builder) {
                builder
                    .addDecoratorPath("/*", "decorator.html")
                    .addExcludedPath("/api/*");
            }
        });
        
        filter.addUrlPatterns("/*");
        return filter;
    }
}
```

## 📝 範例內容頁面

建立 `/hello.html`：

```html
<html>
<head>
    <title>Hello World</title>
    <meta name="description" content="一個簡單的頁面">
</head>
<body>
    <h2>歡迎來到我的網站</h2>
    <p>這是一個使用 <strong>Sitemesh 3.2.2</strong> 裝飾的頁面！</p>
    
    <ul>
        <li>自動套用佈局</li>
        <li>保持內容與樣式分離</li>
        <li>支援多重裝飾器</li>
    </ul>
</body>
</html>
```

## 🚀 最佳實踐

1. **效能優化**: 使用快取機制來提升裝飾器載入速度
2. **SEO 友善**: 確保裝飾器包含適當的 meta 標籤
3. **響應式設計**: 建立適應不同裝置的裝飾器
4. **錯誤處理**: 設定適當的錯誤頁面裝飾器
5. **安全性**: 排除敏感路徑避免被裝飾

---

*此配置指南基於 Sitemesh 3.2.2 官方文件整理，適用於 Java 17+ 和 Jakarta EE 10 環境*
